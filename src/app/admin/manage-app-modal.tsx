"use client";

import React, { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BasicInfoSection, AppFormData } from "./forms/basic-info-section";
import { SecurityFeatureCard } from "./forms/security-feature-card";
import { EmergencyLinksSection } from "./forms/emergency-links-section";
import { InstructionsSection } from "./forms/instructions-section";
import { App, AppType } from "@/core.constants";
import {
  createApp,
  updateApp,
  uploadInstructionImage,
  uploadAppLogo,
} from "@/api/app-api";

const appSchema = z.object({
  name: z.string().optional(),
  type: z.nativeEnum(AppType),
  appLogo: z.string().optional(),

  yubikeys: z.object({
    enabled: z.boolean(),
    link: z
      .string()
      .optional()
      .refine((val) => !val || z.string().url().safeParse(val).success, {
        message: "Must be a valid URL",
      }),
    description: z.string().optional(),
  }),
  phoneNumber: z.object({
    enabled: z.boolean(),
    link: z
      .string()
      .optional()
      .refine((val) => !val || z.string().url().safeParse(val).success, {
        message: "Must be a valid URL",
      }),
    description: z.string().optional(),
  }),
  backup: z.object({
    enabled: z.boolean(),
    link: z
      .string()
      .optional()
      .refine((val) => !val || z.string().url().safeParse(val).success, {
        message: "Must be a valid URL",
      }),
    description: z.string().optional(),
  }),
  pin: z.object({
    enabled: z.boolean(),
    link: z
      .string()
      .optional()
      .refine((val) => !val || z.string().url().safeParse(val).success, {
        message: "Must be a valid URL",
      }),
    description: z.string().optional(),
  }),
  healthAccount: z
    .string()
    .optional()
    .refine((val) => !val || z.string().url().safeParse(val).success, {
      message: "Must be a valid URL",
    }),
  recommendations: z.string().optional(),
  emergency: z.object({
    links: z.array(
      z.object({
        link: z
          .string()
          .optional()
          .refine((val) => !val || z.string().url().safeParse(val).success, {
            message: "Must be a valid URL",
          }),
        description: z.string().optional(),
        label: z.string().optional(),
      })
    ),
    instructions: z.array(
      z.object({
        description: z.string().optional(),
        imageLink: z.string().optional(),
      })
    ),
  }),
});

interface ManageAppModalProps {
  isOpen: boolean;
  onClose: () => void;
  app?: App;
  onSave: () => void;
}

export const ManageAppModal = ({
  isOpen,
  onClose,
  app,
  onSave,
}: ManageAppModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingImages, setUploadingImages] = useState<Set<number>>(
    new Set()
  );
  const [uploadingLogo, setUploadingLogo] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<AppFormData>({
    resolver: zodResolver(appSchema),
    defaultValues: {
      name: "",
      type: AppType.PASSWORD_MANAGERS,
      appLogo: "",
      yubikeys: { enabled: false, link: "", description: "" },
      phoneNumber: { enabled: false, link: "", description: "" },
      backup: { enabled: false, link: "", description: "" },
      pin: { enabled: false, link: "", description: "" },
      healthAccount: "",
      recommendations: "",
      emergency: {
        links: [{ link: "", description: "", label: "" }],
        instructions: [{ description: "", imageLink: "" }],
      },
    },
  });

  // Debug form errors
  React.useEffect(() => {
    if (Object.keys(errors).length > 0) {
      console.log("Form validation errors:", errors);
    }
  }, [errors]);

  const {
    fields: emergencyLinks,
    append: appendEmergencyLink,
    remove: removeEmergencyLink,
  } = useFieldArray({
    control,
    name: "emergency.links",
  });

  const {
    fields: instructions,
    append: appendInstruction,
    remove: removeInstruction,
  } = useFieldArray({
    control,
    name: "emergency.instructions",
  });

  useEffect(() => {
    if (app) {
      reset({
        name: app.name,
        type: app.type,
        appLogo: app.appLogo ?? "",

        yubikeys: {
          enabled: app.yubikeys?.enabled ?? false,
          link: app.yubikeys?.link ?? "",
          description: app.yubikeys?.description ?? "",
        },
        phoneNumber: {
          enabled: app.phoneNumber?.enabled ?? false,
          link: app.phoneNumber?.link ?? "",
          description: app.phoneNumber?.description ?? "",
        },
        backup: {
          enabled: app.backup?.enabled ?? false,
          link: app.backup?.link ?? "",
          description: app.backup?.description ?? "",
        },
        pin: {
          enabled: app.pin?.enabled ?? false,
          link: app.pin?.link ?? "",
          description: app.pin?.description ?? "",
        },
        healthAccount: app.healthAccount ?? "",
        recommendations: app.recommendations ?? "",
        emergency: app.emergency,
      });
    } else {
      reset({
        name: "",
        type: AppType.PASSWORD_MANAGERS,
        appLogo: "",

        yubikeys: { enabled: false, link: "", description: "" },
        phoneNumber: { enabled: false, link: "", description: "" },
        backup: { enabled: false, link: "", description: "" },
        pin: { enabled: false, link: "", description: "" },
        healthAccount: "",
        recommendations: "",
        emergency: {
          links: [{ link: "", description: "", label: "" }],
          instructions: [{ description: "", imageLink: "" }],
        },
      });
    }
  }, [app, reset]);

  const handleImageUpload = async (
    file: File,
    instructionIndex: number
  ): Promise<void> => {
    setUploadingImages((prev) => new Set(prev).add(instructionIndex));

    try {
      const fileName = `instruction_${Date.now()}_${file.name}`;
      const imageUrl = await uploadInstructionImage(file, fileName);
      setValue(
        `emergency.instructions.${instructionIndex}.imageLink`,
        imageUrl
      );
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setUploadingImages((prev) => {
        const newSet = new Set(prev);
        newSet.delete(instructionIndex);
        return newSet;
      });
    }
  };

  const handleLogoUpload = async (file: File): Promise<void> => {
    setUploadingLogo(true);

    try {
      const fileName = `logo_${Date.now()}_${file.name}`;
      const logoUrl = await uploadAppLogo(file, fileName);
      setValue("appLogo", logoUrl);
    } catch (error) {
      console.error("Error uploading logo:", error);
    } finally {
      setUploadingLogo(false);
    }
  };

  const onSubmit = async (data: AppFormData) => {
    console.log("Form submitted with data:", data);
    setIsLoading(true);
    try {
      console.log("Processed app data:", data);

      if (app) {
        console.log("Updating app with ID:", app.id);
        // @ts-expect-error note
        await updateApp(app.id, data);
      } else {
        console.log("Creating new app");
        // @ts-expect-error note
        await createApp(data);
      }
      onSave();
    } catch (error) {
      console.error("Error saving app:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            {app ? "Edit App" : "Create New App"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <BasicInfoSection
            register={register}
            errors={errors}
            watch={watch}
            setValue={setValue}
            onLogoUpload={handleLogoUpload}
            uploadingLogo={uploadingLogo}
          />

          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2">
              Account Management
            </h3>
            <div className="space-y-2">
              <Label htmlFor="healthAccount" className="text-sm font-medium">
                Health Account Link (Optional)
              </Label>
              <Input
                id="healthAccount"
                {...register("healthAccount")}
                placeholder="Link to access devices connected to account"
                className="h-11"
              />
              {errors.healthAccount && (
                <p className="text-sm text-red-600">
                  {errors.healthAccount.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2">
              Security Features
            </h3>

            <SecurityFeatureCard
              title="Yubikey Support"
              icon="🔑"
              fieldName="yubikeys"
              linkLabel="Registration Link"
              linkPlaceholder="Link to register Yubikey"
              switchLabel="Yubikeys Supported"
              register={register}
              errors={errors}
              watch={watch}
              setValue={setValue}
            />

            <SecurityFeatureCard
              title="Phone Number Requirement"
              icon="�"
              fieldName="phoneNumber"
              linkLabel="Removal Instructions Link"
              linkPlaceholder="Link to remove phone number"
              switchLabel="Phone Number Required"
              register={register}
              errors={errors}
              watch={watch}
              setValue={setValue}
            />

            <SecurityFeatureCard
              title="PIN Support"
              icon="🔢"
              fieldName="pin"
              linkLabel="Setup Link"
              linkPlaceholder="Link to setup PIN/Passkey"
              switchLabel="PIN/Passkey Available"
              register={register}
              errors={errors}
              watch={watch}
              setValue={setValue}
            />

            <SecurityFeatureCard
              title="Backup Support"
              icon="💾"
              fieldName="backup"
              linkLabel="Download Link"
              linkPlaceholder="Link to download backup/recovery codes"
              switchLabel="Backup Available"
              register={register}
              errors={errors}
              watch={watch}
              setValue={setValue}
            />
          </div>

          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 border-b pb-2">
              Emergency Information
            </h3>

            <EmergencyLinksSection
              register={register}
              errors={errors}
              emergencyLinks={emergencyLinks}
              appendEmergencyLink={appendEmergencyLink}
              removeEmergencyLink={removeEmergencyLink}
            />

            <InstructionsSection
              register={register}
              errors={errors}
              watch={watch}
              instructions={instructions}
              appendInstruction={appendInstruction}
              removeInstruction={removeInstruction}
              onImageUpload={handleImageUpload}
              uploadingImages={uploadingImages}
            />
          </div>

          <div className="flex justify-end gap-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="px-6"
              onClick={() => console.log("Submit button clicked")}
            >
              {isLoading ? "Saving..." : app ? "Update App" : "Create App"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
