"use client";

import React from "react";
import {
  UseFormRegister,
  FieldErrors,
  UseFormWatch,
  UseFormSetValue,
} from "react-hook-form";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { AppFormData } from "./basic-info-section";

interface SecurityFeatureCardProps {
  title: string;
  icon: string;
  fieldName: "yubikeys" | "phoneNumber" | "backup" | "pin";
  linkLabel: string;
  linkPlaceholder: string;
  switchLabel: string;
  register: UseFormRegister<AppFormData>;
  errors: FieldErrors<AppFormData>;
  watch: UseFormWatch<AppFormData>;
  setValue: UseFormSetValue<AppFormData>;
}

export const SecurityFeatureCard = ({
  title,
  icon,
  fieldName,
  linkLabel,
  linkPlaceholder,
  switchLabel,
  register,
  errors,
  watch,
  setValue,
}: SecurityFeatureCardProps) => {
  return (
    <div className="bg-gray-50 dark:bg-gray-800 border rounded-lg p-6">
      <h4 className="font-semibold mb-4 text-gray-900 dark:text-gray-100">
        {icon} {title}
      </h4>
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Switch
              checked={watch(`${fieldName}.enabled`)}
              onCheckedChange={(checked) =>
                setValue(`${fieldName}.enabled`, checked)
              }
            />
            <Label className="text-sm font-medium">{switchLabel}</Label>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor={`${fieldName}.link`} className="text-sm font-medium">
            {linkLabel}
          </Label>
          <Input
            id={`${fieldName}.link`}
            {...register(`${fieldName}.link`)}
            placeholder={linkPlaceholder}
            className="h-11"
          />
          {errors[fieldName]?.link && (
            <p className="text-sm text-red-600">
              {errors[fieldName]?.link?.message}
            </p>
          )}
        </div>
      </div>

      {(fieldName === "phoneNumber" ||
        fieldName === "yubikeys" ||
        fieldName === "backup" ||
        fieldName === "pin") && (
        <div className="mt-4 space-y-2">
          <Label
            htmlFor={`${fieldName}.description`}
            className="text-sm font-medium"
          >
            Description (Optional)
          </Label>
          <Textarea
            id={`${fieldName}.description`}
            {...register(`${fieldName}.description`)}
            placeholder={
              fieldName === "phoneNumber"
                ? "Enter phone number requirements or additional notes"
                : fieldName === "yubikeys"
                ? "Enter Yubikey setup instructions or additional notes"
                : fieldName === "backup"
                ? "Enter backup/recovery setup instructions or additional notes"
                : "Enter PIN setup instructions or additional notes"
            }
            className="min-h-[80px]"
          />
          {errors[fieldName]?.description && (
            <p className="text-sm text-red-600">
              {errors[fieldName]?.description?.message}
            </p>
          )}
        </div>
      )}
    </div>
  );
};
